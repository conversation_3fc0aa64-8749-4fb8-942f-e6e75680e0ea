import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-4 focus-visible:ring-strategy-blue focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 touch-manipulation",
  {
    variants: {
      variant: {
        default:
          "bg-strategy-blue text-white hover:bg-strategy-blue-600 shadow-md hover:shadow-lg transition-all duration-200",
        accent:
          "bg-insight-gold text-charcoal hover:bg-insight-gold-600 shadow-md hover:shadow-lg transition-all duration-200 font-semibold",
        "accent-contrast":
          "bg-strategy-blue-600 text-white hover:bg-strategy-blue-700 shadow-md hover:shadow-lg transition-all duration-200 font-semibold border-2 border-insight-gold hover:border-insight-gold-600",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-cloud-grey bg-white hover:bg-cloud-grey-300 hover:text-charcoal",
        secondary: "bg-cloud-grey text-charcoal hover:bg-cloud-grey-300",
        ghost: "hover:bg-cloud-grey-200 hover:text-charcoal",
        link: "text-strategy-blue underline-offset-4 hover:underline",
      },
      size: {
        default: "h-11 px-4 py-2 min-w-[44px]",
        sm: "h-11 rounded-md px-3 min-w-[44px]",
        lg: "h-14 rounded-md px-8 text-base font-semibold min-w-[44px]",
        icon: "h-11 w-11 min-w-[44px] min-h-[44px]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />
  },
)
Button.displayName = "Button"

export { Button, buttonVariants }
